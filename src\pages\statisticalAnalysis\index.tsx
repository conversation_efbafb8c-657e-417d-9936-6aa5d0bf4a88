import * as React from 'react';
import { Select, Tooltip } from 'antd';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import style from './index.module.less';
// 导入图表组件
import {
  EnergyTrendChart, // 能耗趋势图表组件
  ProductEnergyChart, // 单品能耗图表组件
  EnergyCompositionChart, // 企业能源构成图表组件
  ProductRankingChart, // 单品排行图表组件
  EnergyStructureChart, // 能源结构图表组件
} from './components';

// 时间维度类型
type TimeDimension = 'year' | 'month';

// 统计周期类型
type StatisticsPeriod = 'recent3Years' | 'recent12Months' | 'recent6Months';

// 趋势数据类型
interface TrendData {
  type: 'up' | 'down';
  value: string;
}

// 指标值类型
interface IndicatorValue {
  value: string;
  trend: TrendData;
}

// 指标数据类型
interface IndicatorData {
  id: number;
  value: string;
  unit: string;
  title: string;
  tooltip: string;
  trend: TrendData;
  icon: React.ReactNode;
}

/**
 * 统计分析
 * @returns React.ReactNode
 */
const StatisticalAnalysis: React.FC = () => {
  // 时间维度状态
  const [timeDimension, setTimeDimension] = React.useState<TimeDimension>('month');

  // 统计周期状态
  const [statisticsPeriod, setStatisticsPeriod] =
    React.useState<StatisticsPeriod>('recent12Months');

  // 时间维度选项
  const timeDimensionOptions: { value: TimeDimension; label: string }[] = [
    { value: 'year', label: '年度统计' },
    { value: 'month', label: '月度统计' },
  ];

  // 根据时间维度获取统计周期选项
  const getStatisticsPeriodOptions: {
    value: StatisticsPeriod;
    label: string;
  }[] = React.useMemo(() => {
    if (timeDimension === 'year') {
      return [{ value: 'recent3Years', label: '近3年' }];
    } else {
      return [
        { value: 'recent12Months', label: '近12个月' },
        { value: 'recent6Months', label: '近6个月' },
      ];
    }
  }, [timeDimension]);

  // 处理时间维度变更
  const handleTimeDimensionChange = (value: TimeDimension) => {
    setTimeDimension(value);
    // 根据时间维度自动设置默认的统计周期
    if (value === 'year') {
      setStatisticsPeriod('recent3Years');
    } else {
      setStatisticsPeriod('recent12Months');
    }
    // 数据更新通过 useMemo 自动响应时间维度变化
  };

  // 处理统计周期变更
  const handleStatisticsPeriodChange = (value: StatisticsPeriod) => {
    setStatisticsPeriod(value);
    // 数据更新通过 useMemo 自动响应统计周期变化
  };

  // 指标卡片数据 - 使用 useMemo 实现数据动态变化
  const indicatorData: IndicatorData[] = React.useMemo(() => {
    // 根据时间维度和统计周期获取对应的数据
    const getIndicatorValues: () => {
      energyConsumption: IndicatorValue;
      energyCost: IndicatorValue;
      energyEfficiency: IndicatorValue;
      unitEnergyConsumption: IndicatorValue;
    } = () => {
      // 这里可以根据 timeDimension 和 statisticsPeriod 返回不同的数据
      // 目前先提供示例数据，后续可以接入真实的 API 数据

      if (timeDimension === 'year') {
        // 年度统计数据
        return {
          energyConsumption: { value: '16,608', trend: { type: 'up', value: '+3.2%' } },
          energyCost: { value: '10,704', trend: { type: 'down', value: '-1.8%' } },
          energyEfficiency: { value: '78.5', trend: { type: 'up', value: '+2.1%' } },
          unitEnergyConsumption: { value: '2.28', trend: { type: 'down', value: '-4.2%' } },
        };
      }

      // 月度统计数据 - 根据统计周期调整
      const baseValues: Record<
        'recent12Months' | 'recent6Months',
        {
          energyConsumption: IndicatorValue;
          energyCost: IndicatorValue;
          energyEfficiency: IndicatorValue;
          unitEnergyConsumption: IndicatorValue;
        }
      > = {
        recent12Months: {
          energyConsumption: { value: '1,384', trend: { type: 'up', value: '+5.6%' } },
          energyCost: { value: '892', trend: { type: 'down', value: '-2.3%' } },
          energyEfficiency: { value: '76.8', trend: { type: 'up', value: '+1.2%' } },
          unitEnergyConsumption: { value: '2.34', trend: { type: 'down', value: '-3.1%' } },
        },
        recent6Months: {
          energyConsumption: { value: '1,425', trend: { type: 'up', value: '+7.2%' } },
          energyCost: { value: '865', trend: { type: 'down', value: '-3.1%' } },
          energyEfficiency: { value: '77.2', trend: { type: 'up', value: '+1.8%' } },
          unitEnergyConsumption: { value: '2.31', trend: { type: 'down', value: '-3.8%' } },
        },
      };

      // 对于月度数据，根据统计周期返回对应数据
      if (statisticsPeriod === 'recent6Months') {
        return baseValues.recent6Months;
      }
      return baseValues.recent12Months; // 默认返回近12个月数据
    };

    const values = getIndicatorValues();
    const timeUnit: string = timeDimension === 'year' ? '年度' : '单月';

    return [
      {
        id: 1,
        value: values.energyConsumption.value,
        unit: '万tce',
        title: `平均综合能耗（${timeUnit}）`,
        tooltip: `统计周期内，各${timeDimension === 'year' ? '年' : '月'}综合能耗的算术平均值`,
        trend: values.energyConsumption.trend,
        icon: (
          <svg
            t="1754014516428"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="15608"
            width="32"
            height="32"
          >
            <path
              d="M564.544 58.048C591.488 25.6 644.032 47.808 639.808 89.6L609.728 384h243.584c36.224 0 55.936 42.24 32.768 69.952l-426.624 512c-26.944 32.384-79.488 10.24-75.2-31.616L414.336 640H170.688a42.688 42.688 0 0 1-32.768-69.952l426.624-512z"
              p-id="15609"
              fill="#8a8a8a"
            ></path>
          </svg>
        ),
      },
      {
        id: 2,
        value: values.energyCost.value,
        unit: '万元',
        title: `平均能源成本（${timeUnit}）`,
        tooltip: `统计周期内，各${timeDimension === 'year' ? '年' : '月'}能源采购成本的算术平均值`,
        trend: values.energyCost.trend,
        icon: (
          <svg
            t="1754014690164"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="25944"
            width="32"
            height="32"
          >
            <path
              d="M930 932h-804c-27.616 0-50-22.392-50-50v-752C76 102.384 98.384 80 126 80S176 102.384 176 130V832h754c27.608 0 50 22.392 50 50s-22.392 50-50 50z"
              p-id="25945"
              fill="#8a8a8a"
            ></path>
            <path
              d="M320 718a49.86 49.86 0 0 1-35.356-14.644c-19.524-19.528-19.524-51.184 0-70.712l136-136A50 50 0 0 1 456 482h185.288l194.356-194.356c19.524-19.528 51.188-19.528 70.712 0 19.528 19.524 19.528 51.184 0 70.712l-209 209a49.98 49.98 0 0 1-35.356 14.644h-185.288l-121.356 121.356A49.86 49.86 0 0 1 320 718z"
              p-id="25946"
              fill="#8a8a8a"
            ></path>
          </svg>
        ),
      },
      {
        id: 3,
        value: values.energyEfficiency.value,
        unit: '%',
        title: '能源利用效率',
        tooltip: '有效能源利用量占总能源投入量的百分比',
        trend: values.energyEfficiency.trend,
        icon: (
          <svg
            t="1754014866533"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="38216"
            width="32"
            height="32"
          >
            <path
              d="M896 294.4c0 20.48-17.92 38.4-38.4 38.4h-537.6c-20.48 0-38.4-17.92-38.4-38.4s17.92-38.4 38.4-38.4h537.6c20.48 0 38.4 17.92 38.4 38.4zM166.4 793.6h691.2c20.48 0 38.4 17.92 38.4 38.4s-17.92 38.4-38.4 38.4h-691.2c-20.48 0-38.4-17.92-38.4-38.4S145.92 793.6 166.4 793.6zM793.6 473.6c0 20.48-17.92 38.4-38.4 38.4h-435.2c-20.48 0-38.4-17.92-38.4-38.4s17.92-38.4 38.4-38.4h435.2c20.48 0 38.4 17.92 38.4 38.4zM691.2 652.8c0 20.48-17.92 38.4-38.4 38.4h-332.8c-20.48 0-38.4-17.92-38.4-38.4s17.92-38.4 38.4-38.4h332.8c20.48 0 38.4 17.92 38.4 38.4z"
              p-id="38217"
              fill="#8a8a8a"
            ></path>
            <path
              d="M166.4 128C186.88 128 204.8 145.92 204.8 166.4v665.6c0 20.48-17.92 38.4-38.4 38.4S128 852.48 128 832v-665.6C128 145.92 145.92 128 166.4 128z"
              p-id="38218"
              fill="#8a8a8a"
            ></path>
          </svg>
        ),
      },
      {
        id: 4,
        value: values.unitEnergyConsumption.value,
        unit: 'tce/万元',
        title: '单位产值能耗',
        tooltip: '每万元产值所消耗的标准煤当量',
        trend: values.unitEnergyConsumption.trend,
        icon: (
          <svg
            t="1754014773916"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="31458"
            width="32"
            height="32"
          >
            <path
              d="M651.648128 791.1424L416.512128 83.1488c-14.1312-42.5984-74.1632-42.5984-88.3072 0L199.180928 471.68H46.540928A46.6304 46.6304 0 0 0 0.000128 518.4a46.6304 46.6304 0 0 0 46.5408 46.72h186.1888a46.5664 46.5664 0 0 0 44.16-31.9488l95.4752-287.5136 235.1104 707.9936c14.144 42.5984 74.176 42.5984 88.32 0l129.024-388.5312h152.64A46.6304 46.6304 0 0 0 1024.000128 518.4a46.6304 46.6304 0 0 0-46.5408-46.72H791.270528a46.5664 46.5664 0 0 0-44.16 31.9488L651.648128 791.1424z"
              fill="#8a8a8a"
              p-id="31459"
            ></path>
          </svg>
        ),
      },
    ];
  }, [timeDimension, statisticsPeriod]); // 依赖项：当时间维度或统计周期变化时重新计算

  return (
    <div className={style['statistical-analysis']}>
      {/* 筛选与导出区（顶部） */}
      <div className={style['filter-export-section']}>
        {/* 左侧：时间维度和统计周期 */}
        <div className={style['filter-controls']}>
          <div className={style['time-dimension']}>
            {/* 时间维度选择 */}
            <span className={style.label}>时间维度：</span>
            <Select
              value={timeDimension}
              onChange={handleTimeDimensionChange}
              options={timeDimensionOptions}
              style={{ width: 120 }}
              size="middle"
            />
          </div>
          <div className={style['period-selection']}>
            {/* 统计周期选择 */}
            <span className={style.label}>统计周期：</span>
            <Select
              value={statisticsPeriod}
              onChange={handleStatisticsPeriodChange}
              options={getStatisticsPeriodOptions}
              style={{ width: 120 }}
              size="middle"
            />
          </div>
        </div>

        {/* 右侧：月报导出 */}
        <div className={style['export-button']}>
          {/* 月报导出按钮 */}
          <span>月报导出</span>
        </div>
      </div>

      {/* 关键指标展示区（顶部下方，卡片式） */}
      <div className={style['key-indicators-section']}>
        {indicatorData.map((indicator) => (
          <div key={indicator.id} className={style['indicator-card']}>
            {/* 右侧图标 */}
            <div className={style['card-icon']}>{indicator.icon}</div>

            {/* 最上层：核心数值 */}
            <div className={style['card-value']}>
              <span className={style['value-number']}>{indicator.value}</span>
              <span className={style['value-unit']}>{indicator.unit}</span>
            </div>

            {/* 中间层：指标定义 + 交互提示 */}
            <div className={style['card-title']}>
              <span className={style['title-text']}>{indicator.title}</span>
              <Tooltip title={indicator.tooltip} placement="top">
                <div className={style['title-tooltip']}>?</div>
              </Tooltip>
            </div>

            {/* 最下层：趋势对比 */}
            <div className={style['card-trend']}>
              <span
                className={indicator.trend.type === 'up' ? style['trend-up'] : style['trend-down']}
              >
                <span className={style['trend-arrow']}>
                  {indicator.trend.type === 'up' ? '↑' : '↓'}
                </span>
                <span className={style['trend-text']}>同比 {indicator.trend.value}</span>
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* 数据可视化区（中间，分两排） */}
      <div className={style['visualization-section']}>
        {/* 第一排：能耗趋势（折线）、单品能耗（柱状 + 平均线） */}
        <div className={style['chart-row']}>
          {/* 能耗趋势 */}
          <EnergyTrendChart />
          {/* 单品能耗 */}
          <ProductEnergyChart />
        </div>

        {/* 第二排：企业能源构成（堆叠柱）、单品排行（横向条）、能源结构（饼图） */}
        <div className={style['chart-row']}>
          {/* 企业能源构成 */}
          <EnergyCompositionChart />
          {/* 单品排行 */}
          <ProductRankingChart />
          {/* 能源结构 */}
          <EnergyStructureChart />
        </div>
      </div>
    </div>
  );
};

export default YTHLocalization.withLocal(
  StatisticalAnalysis,
  locales,
  YTHLocalization.getLanguage(),
);
