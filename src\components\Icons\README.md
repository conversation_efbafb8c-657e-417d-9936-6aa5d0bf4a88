# 统计分析图标组件

## 概述

这个模块提供了统计分析页面使用的优化后的SVG图标组件。相比于原来的内联SVG，这些组件具有以下优势：

## 优化效果

### 🚀 性能优化
- **减少代码体积**：从每个图标 ~20 行代码减少到 1 行
- **组件复用**：图标可以在多个地方复用
- **渲染优化**：避免每次渲染都重新创建SVG元素

### 🎨 可定制性
- **动态颜色**：支持通过 `color` 属性自定义颜色
- **灵活尺寸**：支持通过 `width` 和 `height` 属性自定义尺寸
- **样式扩展**：支持通过 `className` 添加自定义样式

### 🛠️ 维护性
- **集中管理**：所有图标集中在一个文件中管理
- **类型安全**：完整的 TypeScript 类型支持
- **一致性**：统一的接口和使用方式

## 使用方法

### 基础使用

```tsx
import { EnergyConsumptionIcon } from '@/components/Icons';

// 默认使用（32x32，灰色）
<EnergyConsumptionIcon />
```

### 自定义属性

```tsx
import { EnergyConsumptionIcon } from '@/components/Icons';

// 自定义颜色和尺寸
<EnergyConsumptionIcon 
  width={24} 
  height={24} 
  color="#1890ff" 
  className="my-icon-class"
/>
```

### 动态使用

```tsx
import { StatisticalIcons, StatisticalIconType } from '@/components/Icons';

const iconType: StatisticalIconType = 'energyConsumption';
const IconComponent = StatisticalIcons[iconType];

<IconComponent color="#52c41a" />
```

## 可用图标

| 图标组件 | 用途 | 图标类型键 |
|---------|------|-----------|
| `EnergyConsumptionIcon` | 综合能耗指标 | `energyConsumption` |
| `EnergyCostIcon` | 能源成本指标 | `energyCost` |
| `EnergyEfficiencyIcon` | 能源效率指标 | `energyEfficiency` |
| `UnitEnergyConsumptionIcon` | 单位能耗指标 | `unitEnergyConsumption` |

## 接口定义

```tsx
interface IconProps {
  width?: number;        // 图标宽度，默认 32
  height?: number;       // 图标高度，默认 32
  color?: string;        // 图标颜色，默认 '#8a8a8a'
  className?: string;    // 自定义CSS类名
}
```

## 迁移指南

### 原来的写法（已优化）
```tsx
icon: (
  <svg viewBox="0 0 1024 1024" width="32" height="32">
    <path d="..." fill="#8a8a8a" />
  </svg>
)
```

### 现在的写法
```tsx
icon: <EnergyConsumptionIcon />
```

## 扩展建议

如果需要添加新的图标：

1. 在 `StatisticalIcons.tsx` 中添加新的图标组件
2. 更新 `StatisticalIcons` 对象和 `StatisticalIconType` 类型
3. 在此文档中更新图标列表

这样可以保持代码的一致性和可维护性。
